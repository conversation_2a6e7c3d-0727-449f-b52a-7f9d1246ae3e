import pandas as pd
import json
import re
import os
import pickle
from typing import List, Dict
from contextlib import contextmanager
import sys
import torch
from tqdm import tqdm
import numpy as np
import faiss
# Thêm import cho chuẩn hóa văn bản và word segmentation
from underthesea import text_normalize, word_tokenize
from transformers import pipeline

# Import BGE-M3
try:
    from FlagEmbedding import BGEM3FlagModel
except ImportError:
    print("Warning: FlagEmbedding not found. Please install: pip install FlagEmbedding")
    BGEM3FlagModel = None

@contextmanager
def suppress_stdout_stderr():
    """Context manager để suppress output"""
    with open(os.devnull, "w") as devnull:
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        try:
            sys.stdout = devnull
            sys.stderr = devnull
            yield
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr

def load_data(train_path='train.json', legal_corpus_path='legal_corpus.json'):
    # Đọc file train.json
    with open(train_path, 'r', encoding='utf-8') as f:
        train_data = json.load(f)
    train_df = pd.DataFrame(train_data)

    # Đọc file legal_corpus.json
    with open(legal_corpus_path, 'r', encoding='utf-8') as f:
        legal_corpus = json.load(f)

    # Chuyển đổi legal_corpus thành DataFrame
    legal_articles = []
    for doc in legal_corpus:
        for article in doc['content']:
            legal_articles.append({
                'doc_id': doc['id'],
                'law_id': doc['law_id'],
                'aid': article['aid'],
                'content_Article': article['content_Article']
            })

    legal_df = pd.DataFrame(legal_articles)

    return train_df, legal_df

def find_enumeration_ranges(content):
    """
    Tìm các khoảng văn bản chứa danh sách liệt kê sau dấu ":"
    Trả về list of tuples (start, end) của các khoảng cần bảo vệ
    """
    enumeration_ranges = []
    
    # Pattern 1: Tìm cấu trúc liệt kê sau dấu : với xuống dòng (\n\n1.)
    pattern1 = r':(?:\s*\n){1,3}\s*1\.\s'
    
    # Pattern 2: Tìm cấu trúc liệt kê sau dấu : không xuống dòng (: 1.)
    pattern2 = r':\s*1\.\s'
    
    # Xử lý cả hai pattern
    patterns = [pattern1, pattern2]
    
    for pattern in patterns:
        for match in re.finditer(pattern, content):
            colon_pos = match.start()
            first_item_start = match.end() - 3  # Vị trí của "1. "
            
            # Tìm phần cuối của danh sách liệt kê
            # Tìm số thứ tự cuối cùng trong danh sách
            current_pos = first_item_start
            last_item_end = first_item_start
            item_number = 1
            
            while True:
                # Tìm item tiếp theo
                if pattern == pattern1:  # Có xuống dòng
                    next_item_pattern = rf'(?:\s*\n)+\s*{item_number + 1}\.\s'
                else:  # Không xuống dòng, tìm trong cùng dòng hoặc dòng tiếp theo
                    next_item_pattern = rf';\s*{item_number + 1}\.\s|(?:\s*\n)+\s*{item_number + 1}\.\s'
                
                next_match = re.search(next_item_pattern, content[current_pos:])
                
                if next_match:
                    # Tìm thấy item tiếp theo
                    item_number += 1
                    current_pos += next_match.end()
                    
                    # Tìm kết thúc của item này (trước item tiếp theo hoặc kết thúc văn bản)
                    if pattern == pattern1:
                        next_next_pattern = rf'(?:\s*\n)+\s*{item_number + 1}\.\s'
                    else:
                        next_next_pattern = rf';\s*{item_number + 1}\.\s|(?:\s*\n)+\s*{item_number + 1}\.\s'
                    
                    next_next_match = re.search(next_next_pattern, content[current_pos:])
                    
                    if next_next_match:
                        last_item_end = current_pos + next_next_match.start()
                    else:
                        # Đây là item cuối cùng, tìm kết thúc của nó
                        remaining_text = content[current_pos:]
                        
                        # Tìm dấu kết thúc đoạn văn
                        if pattern == pattern1:
                            end_pattern = r'\n\s*\n\s*(?!\d+\.)'
                        else:
                            end_pattern = r'\.(?:\s*\n\s*\n|\s*$)'
                        
                        end_match = re.search(end_pattern, remaining_text)
                        
                        if end_match:
                            last_item_end = current_pos + end_match.start() + 1  # +1 để bao gồm dấu chấm
                        else:
                            # Nếu không tìm thấy, lấy hết phần còn lại
                            last_item_end = len(content)
                        break
                else:
                    # Không tìm thấy item tiếp theo, kết thúc danh sách
                    remaining_text = content[current_pos:]
                    
                    # Tìm dấu kết thúc đoạn văn
                    if pattern == pattern1:
                        end_pattern = r'\n\s*\n\s*(?!\d+\.)'
                    else:
                        end_pattern = r'\.(?:\s*\n\s*\n|\s*$)'
                    
                    end_match = re.search(end_pattern, remaining_text)
                    
                    if end_match:
                        last_item_end = current_pos + end_match.start() + 1  # +1 để bao gồm dấu chấm
                    else:
                        # Nếu không tìm thấy, lấy hết phần còn lại
                        last_item_end = len(content)
                    break
            
            # Lưu khoảng của toàn bộ cấu trúc liệt kê (từ trước dấu : đến hết danh sách)
            # Tìm điểm bắt đầu của câu chứa dấu :
            text_before_colon = content[:colon_pos]
            sentence_start = max(
                text_before_colon.rfind('\n\n'),
                text_before_colon.rfind('. '),
                0
            )
            if sentence_start > 0 and content[sentence_start:sentence_start+2] == '\n\n':
                sentence_start += 2
            elif sentence_start > 0 and content[sentence_start:sentence_start+2] == '. ':
                sentence_start += 2
                
            enumeration_ranges.append((sentence_start, last_item_end))
    
    # Tìm các chuỗi đánh số phân cấp như 1.1., 1.2., 1.3.
    hierarchical_pattern = r'\b(\d+\.\d+\.)\s'
    hierarchical_matches = list(re.finditer(hierarchical_pattern, content))
    
    if hierarchical_matches:
        # Nhóm các matches theo prefix (1.1, 1.2 cùng group với nhau)
        groups = {}
        for match in hierarchical_matches:
            number = match.group(1)
            prefix = number.split('.')[0]  # Lấy số đầu tiên (1 từ 1.1.)
            
            if prefix not in groups:
                groups[prefix] = []
            groups[prefix].append(match)
        
        # Xử lý từng group
        for prefix, matches in groups.items():
            if len(matches) >= 2:  # Chỉ bảo vệ nếu có ít nhất 2 items
                # Sắp xếp theo vị trí
                matches.sort(key=lambda x: x.start())
                
                # Tìm điểm bắt đầu (từ đầu item đầu tiên)
                start_pos = matches[0].start()
                
                # Tìm điểm kết thúc (sau item cuối cùng)
                last_match = matches[-1]
                last_pos = last_match.end()
                
                # Tìm kết thúc của content của item cuối cùng
                remaining_text = content[last_pos:]
                
                # Tìm điểm kết thúc hợp lý
                end_patterns = [
                    r'\n\s*\n\s*(?!\d+\.\d+\.)',  # Đoạn mới không phải hierarchical numbering
                    r'\n\s*\n\s*(?:Điều|Chương|Mục|\d+\.)',  # Điều, Chương, Mục mới
                    r'$'  # Kết thúc văn bản
                ]
                
                end_pos = len(content)  # Mặc định
                for end_pattern in end_patterns:
                    end_match = re.search(end_pattern, remaining_text)
                    if end_match:
                        end_pos = last_pos + end_match.start()
                        break
                
                enumeration_ranges.append((start_pos, end_pos))
    
    return enumeration_ranges

def chunk_legal_article(content, min_length=256, max_length=1024):
    # Tìm các khoảng chứa danh sách liệt kê cần bảo vệ
    enumeration_ranges = find_enumeration_ranges(content)
    
    # Pattern cải thiện để tránh nhầm lẫn với ngày tháng, số đánh phân cấp và số trong tiêu đề
    # Loại trừ: ngày tháng, số phân cấp (1.1.), số sau các từ khóa pháp lý
    pattern = r'(?<![\"\'""::\w])(?<!ngày\s)(?<!tháng\s)(?<!năm\s)(?<!Ngày\s)(?<!Tháng\s)(?<!Năm\s)(?<!\d\.)(?<!Điều\s)(?<!điều\s)(?<!Khoản\s)(?<!khoản\s)(?<!Chương\s)(?<!chương\s)(?<!Mục\s)(?<!mục\s)(?<!Tiêu\schí\s)(?<!tiêu\schí\s)(?<!Phần\s)(?<!phần\s)(?<!Bộ\s)(?<!bộ\s)(?<!Tờ\s)(?<!tờ\s)(?<!Số\s)(?<!số\s)(\b\d+\.\s)(?!\d)'
    
    # Tìm tất cả vị trí bắt đầu của các điểm số
    all_matches = list(re.finditer(pattern, content))
    
    # Loại bỏ các matches nằm trong khoảng liệt kê
    matches = []
    for match in all_matches:
        match_pos = match.start()
        is_in_enumeration = False
        
        for start, end in enumeration_ranges:
            if start <= match_pos <= end:
                is_in_enumeration = True
                break
        
        if not is_in_enumeration:
            matches.append(match)
    
    if not matches:
        # Nếu không có điểm số nào, kiểm tra độ dài và chia nếu cần
        if len(content.strip()) <= max_length:
            return [content.strip()]
        else:
            # Chia theo max_length nếu quá dài với logic cắt thông minh
            chunks = []
            text = content.strip()
            
            while len(text) > max_length:
                # Tìm vị trí cắt tốt nhất gần max_length
                cut_pos = max_length
                
                # Cố gắng cắt ở dấu chấm câu trước (ưu tiên cao nhất)
                sentence_endings = ['. ', '! ', '? ', '.\n', '!\n', '?\n']
                best_cut_pos = -1
                
                # Tìm dấu chấm câu gần nhất từ max_length về phía trước
                for i in range(min(cut_pos, len(text) - 2), max(min_length, 0), -1):
                    for ending in sentence_endings:
                        if text[i:i+len(ending)] == ending:
                            # Kiểm tra xem phần sau có đủ dài không
                            remaining_length = len(text) - (i + 1)
                            if remaining_length >= min_length:
                                best_cut_pos = i + 1  # Giữ lại dấu chấm
                                break
                    if best_cut_pos != -1:
                        break
                
                # Nếu không tìm được dấu chấm câu, cắt ở khoảng trắng
                if best_cut_pos == -1:
                    for i in range(min(cut_pos, len(text) - 1), max(min_length, 0), -1):
                        if text[i] == ' ':
                            # Kiểm tra xem phần sau có đủ dài không
                            remaining_length = len(text) - i
                            if remaining_length >= min_length:
                                best_cut_pos = i
                                break
                
                # Nếu vẫn không tìm được vị trí tốt hoặc phần sau quá ngắn, không cắt
                if best_cut_pos == -1 or best_cut_pos <= min_length:
                    # Không cắt, giữ nguyên text dài
                    chunks.append(text)
                    break
                
                # Cắt chunk và thêm vào danh sách
                chunk = text[:best_cut_pos].strip()
                chunks.append(chunk)
                text = text[best_cut_pos:].strip()
            
            # Thêm phần còn lại nếu có
            if text:
                chunks.append(text)
            
            return chunks
    
    chunks = []
    i = 0
    
    # Xử lý phần mở đầu không đánh số
    first_match_start = matches[0].start()
    opening_part = content[:first_match_start].strip()
    
    if opening_part:
        # Có phần mở đầu, kết hợp với chunk đầu tiên
        if i + 1 < len(matches):
            end_pos = matches[i + 1].start()
        else:
            end_pos = len(content)
        
        # Lấy chunk đầu tiên kèm phần mở đầu
        first_chunk_with_opening = content[:end_pos].strip()
        chunks.append(first_chunk_with_opening)
        i = 1
    
    while i < len(matches):
        start_pos = matches[i].start()
        current_chunk = ""
        
        # Tìm vị trí kết thúc ban đầu của chunk hiện tại
        if i + 1 < len(matches):
            end_pos = matches[i + 1].start()
        else:
            end_pos = len(content)
        
        # Lấy content của chunk hiện tại
        current_chunk = content[start_pos:end_pos].strip()
        
        # Kiểm tra và kết hợp các chunk tiếp theo để đạt min_length
        j = i + 1
        while len(current_chunk) < min_length and j < len(matches):
            # Mở rộng chunk để bao gồm chunk tiếp theo
            if j + 1 < len(matches):
                next_end_pos = matches[j + 1].start()
            else:
                next_end_pos = len(content)
            
            current_chunk = content[start_pos:next_end_pos].strip()
            j += 1
        
        # Nếu chunk vẫn nhỏ hơn min_length và đã hết matches, lấy hết content còn lại
        if len(current_chunk) < min_length and j >= len(matches):
            current_chunk = content[start_pos:].strip()
        
        # Kiểm tra final: nếu chunk vẫn quá ngắn, kết hợp với chunk trước đó
        if len(current_chunk) < min_length:
            if chunks:
                # Kết hợp với chunk trước đó, thậm chí có thể vượt max_length
                chunks[-1] = chunks[-1] + " " + current_chunk
                # Cập nhật chỉ số để bỏ qua các matches đã được xử lý
                i = j if j > i + 1 else i + 1
                continue
            else:
                # Nếu không có chunk trước đó, vẫn phải giữ chunk này
                pass
        
        # Kiểm tra nếu chunk quá dài, cắt ở max_length
        if len(current_chunk) > max_length:
            # Tìm vị trí cắt tốt nhất gần max_length
            cut_pos = max_length
            
            # Cố gắng cắt ở dấu chấm câu trước (ưu tiên cao nhất)
            sentence_endings = ['. ', '! ', '? ', '.\n', '!\n', '?\n']
            best_cut_pos = -1
            
            # Tìm dấu chấm câu gần nhất từ max_length về phía trước
            for k in range(min(cut_pos, len(current_chunk) - 2), max(min_length, 0), -1):
                for ending in sentence_endings:
                    if current_chunk[k:k+len(ending)] == ending:
                        # Kiểm tra xem phần sau có đủ dài không
                        remaining_length = len(current_chunk) - (k + 1)
                        if remaining_length >= min_length:
                            best_cut_pos = k + 1  # Giữ lại dấu chấm
                            break
                if best_cut_pos != -1:
                    break
            
            # Nếu không tìm được dấu chấm câu, cắt ở khoảng trắng
            if best_cut_pos == -1:
                for k in range(min(cut_pos, len(current_chunk) - 1), max(min_length, 0), -1):
                    if current_chunk[k] == ' ':
                        # Kiểm tra xem phần sau có đủ dài không
                        remaining_length = len(current_chunk) - k
                        if remaining_length >= min_length:
                            best_cut_pos = k
                            break
            
            # Nếu vẫn không tìm được vị trí tốt hoặc phần sau quá ngắn, không cắt
            if best_cut_pos == -1 or best_cut_pos <= min_length:
                # Không cắt, giữ nguyên chunk dài
                chunks.append(current_chunk)
            else:
                # Chia chunk thành 2 phần (không có overlap)
                first_chunk = current_chunk[:best_cut_pos].strip()
                remaining_content = current_chunk[best_cut_pos:].strip()
                
                chunks.append(first_chunk)
                
                # Xử lý phần còn lại
                if len(remaining_content) >= min_length:
                    # Kiểm tra nếu remaining_content vẫn quá dài, chia đệ quy
                    if len(remaining_content) > max_length:
                        # Chia đệ quy phần còn lại
                        remaining_chunks = chunk_legal_article(remaining_content, min_length, max_length)
                        chunks.extend(remaining_chunks)
                    else:
                        chunks.append(remaining_content)
                else:
                    # Nếu phần còn lại quá ngắn, kết hợp với chunk vừa thêm
                    if remaining_content.strip():
                        chunks[-1] = chunks[-1] + " " + remaining_content.strip()
        else:
            # Thêm chunk mà không có overlap
            chunks.append(current_chunk)
        
        # Cập nhật chỉ số để bỏ qua các matches đã được xử lý
        i = j if j > i + 1 else i + 1
    
    return chunks

def process_legal_corpus_with_chunks(legal_df, min_length=256, max_length=1024):
    processed_data = []
    
    for idx, row in legal_df.iterrows():
        chunks = chunk_legal_article(row['content_Article'], min_length, max_length)
        
        for chunk_idx, chunk in enumerate(chunks):
            processed_data.append({
                'doc_id': row['doc_id'],
                'law_id': row['law_id'],
                'aid': row['aid'],
                'chunk_id': chunk_idx,
                'original_content': row['content_Article'],
                'chunk_content': chunk,
                'chunk_length': len(chunk)
            })
    
    return pd.DataFrame(processed_data)

train_df, legal_df = load_data('train.json', 'legal_corpus.json')

# Examine the data
print(f"Train data shape: {train_df.shape}")
print(f"Legal corpus shape: {legal_df.shape}")

# Display sample data
print("\nSample question:")
print(train_df['question'].iloc[0])

print("\nSample legal article:")
print(legal_df['content_Article'].iloc[0])

legal_df_with_chunks = process_legal_corpus_with_chunks(legal_df, min_length=256, max_length=1024)
print(f"Legal corpus with chunks shape: {legal_df_with_chunks.shape}")

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    BitsAndBytesConfig,
    pipeline
)
import gc

# Bước 1: Cấu hình quantization
print("Bước 1: Cấu hình quantization...")
quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,                      # Sử dụng 4-bit quantization
    bnb_4bit_compute_dtype=torch.bfloat16,   # Kiểu dữ liệu tính toán
    bnb_4bit_use_double_quant=True,         # Sử dụng double quantization
    bnb_4bit_quant_type="nf4"               # Kiểu quantization (nf4 hoặc fp4)
)

# Bước 2: Thiết lập device
print("Bước 2: Kiểm tra device...")
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Sử dụng device: {device}")

# Bước 3: Load tokenizer
print("Bước 3: Load tokenizer...")
model_name = "Viet-Mistral/Vistral-7B-Chat"
tokenizer = AutoTokenizer.from_pretrained(
    model_name,
    trust_remote_code=True,
    use_fast=False  # Tránh lỗi với một số tokenizer
)

# Thêm pad_token nếu không có
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

# Bước 4: Load model với quantization
print("Bước 4: Load model với quantization...")
print("Đang tải model... (có thể mất vài phút)")

model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=quantization_config,
    torch_dtype=torch.float16,
    device_map="auto",                      # Tự động phân phối trên GPU/CPU
    trust_remote_code=True,
    low_cpu_mem_usage=True,                 # Giảm sử dụng RAM
    offload_folder="./offload",             # Thư mục tạm để offload
    offload_state_dict=True                 # Offload state dict
)

print("Model đã được load thành công!")

# Bước 5: Kiểm tra memory usage
if torch.cuda.is_available():
    print(f"GPU memory allocated: {torch.cuda.memory_allocated()/1024**3:.2f} GB")
    print(f"GPU memory cached: {torch.cuda.memory_reserved()/1024**3:.2f} GB")

# Bước 6: Tạo pipeline để sử dụng
print("Bước 6: Tạo pipeline...")
generator = pipeline(
    "text-generation",
    model=model,
    tokenizer=tokenizer,
    torch_dtype=torch.float16,
    device_map="auto"
)